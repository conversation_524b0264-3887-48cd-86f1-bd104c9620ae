<?php

define("IN_SITE", true);
require_once(__DIR__."/../../config.php");
require_once(__DIR__."/../../libs/db.php");
require_once(__DIR__."/../../libs/lang.php");
require_once(__DIR__."/../../libs/helper.php");
 

if ($CMSNT->site('status_demo') != 0) {
    $data = json_encode([
        'status'    => 'error',
        'msg'       => __('This function cannot be used because this is a demo site')
    ]);
    die($data);
}
if(!isset($_POST['action'])){
    $data = json_encode([
        'status'    => 'error',
        'msg'       => __('The Request Not Found')
    ]);
    die($data);   
}

 

if($_POST['action'] == 'loadStatusInvoice'){
    if (empty($_POST['trans_id'])) {
        die(json_encode(['status' => 'error', 'msg' => __('Trans ID does not exist in the system')]));
    }
    if (!$row = $CMSNT->get_row("SELECT * FROM `invoices` WHERE `trans_id` = '".check_string($_POST['trans_id'])."' ")) {
        die(json_encode(['status' => 'error', 'msg' => __('Trans ID does not exist in the system')]));
    }
    die(json_encode([
        'data'  => [
            'status'   => $row['status']
        ],
        'status' => 'success', 
        'msg' => ''
    ]));
}

// HIỂN THỊ THÔNG BÁO KHI NẠP TIỀN
if($_POST['action'] == 'notication_topup'){
    if (empty($_POST['token'])) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')]));
    }
    if (!$getUser = $CMSNT->get_row("SELECT * FROM `users` WHERE `token` = '".check_string($_POST['token'])."' AND `banned` = 0 ")) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')]));
    }
    if(!$row = $CMSNT->get_row(" SELECT * FROM `payment_bank` WHERE `notication` = 0 AND `user_id` = '".$getUser['id']."' ")){
        die(json_encode(['status' => 'error', 'msg' => __('Không có lịch sử nạp tiền gần đây')]));
    }
    $CMSNT->update('payment_bank', [
        'notication'    => 1
    ], " `id` = '".$row['id']."' ");
    die(json_encode([
        'status' => 'success', 
        'msg' => __('Nạp tiền thành công').' '.format_currency($row['received'])
    ]));
}
if($_POST['action'] == 'notication_topup_momo'){
    if (empty($_POST['token'])) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')]));
    }
    if (!$getUser = $CMSNT->get_row("SELECT * FROM `users` WHERE `token` = '".check_string($_POST['token'])."' AND `banned` = 0 ")) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')]));
    }
    if(!$row = $CMSNT->get_row(" SELECT * FROM `payment_momo` WHERE `notication` = 0 AND `user_id` = '".$getUser['id']."' ")){
        die(json_encode(['status' => 'error', 'msg' => __('Không có lịch sử nạp tiền gần đây')]));
    }
    $CMSNT->update('payment_momo', [
        'notication'    => 1
    ], " `id` = '".$row['id']."' ");
    die(json_encode([
        'status' => 'success', 
        'msg' => __('Nạp tiền thành công').' '.format_currency($row['received'])
    ]));
}
 


die(json_encode([
    'status'    => 'error',
    'msg'       => __('Invalid data')
]));