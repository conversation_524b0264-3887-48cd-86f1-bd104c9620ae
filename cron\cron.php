<?php

    define("IN_SITE", true);
    require_once(__DIR__.'/../libs/db.php');
    require_once(__DIR__.'/../config.php');
    require_once(__DIR__.'/../libs/lang.php');
    require_once(__DIR__.'/../libs/helper.php');
    require_once(__DIR__.'/../libs/sendEmail.php');
    $CMSNT = new DB();

    /* START CHỐNG SPAM */
    if (time() > $CMSNT->site('check_time_cron_cron')) {
        if (time() - $CMSNT->site('check_time_cron_cron') < 3) {
            die('Thao tác qu<PERSON> nhanh, vui lòng thử lại sau!');
        }
    }
    $CMSNT->update("settings", [
        'value' => time()
    ], " `name` = 'check_time_cron_cron' ");



    

    foreach ($CMSNT->get_list("SELECT * FROM cron_jobs WHERE expires_at <= '".gettime()."' AND status != 'expired'") as $cron) {
        $getUser = $CMSNT->get_row(" SELECT * FROM `users` WHERE `id` = '".$cron['user_id']."' ");
        // Tạm dừng các CRON hết hạn
        $CMSNT->update('cron_jobs', [
            'status'    => 'expired',
            'updated_at'    => gettime()
        ], " `id` = '".$cron['id']."' ");

        // Gửi email cho từng người dùng có cron job hết hạn
        $replacements = [
            '{domain}' => $_SERVER['SERVER_NAME'],
            '{title}' => $CMSNT->site('title'),
            '{username}' => $getUser['username'],
            '{link_cron}' => $cron['link_cron'],
            '{server}' => getRowRealtime('cron_server', $cron['server_id'], 'name'),
            '{link_renew}' => '<a target="_blank" href="'.base_url('?action=cron-job-renew&id='.$cron['trans_id']).'">'.base_url('?action=cron-job-renew&id='.$cron['trans_id']).'</a>',
            '{time}' => gettime()
        ];
        // Lấy template nội dung và tiêu đề email
        $content = $CMSNT->site('email_temp_content_thong_bao_link_het_han');
        $subject = $CMSNT->site('email_temp_subject_thong_bao_link_het_han');
        // Thay thế các giá trị trong content và subject
        foreach ($replacements as $key => $value) {
            $content = str_replace($key, $value, $content);
            $subject = str_replace($key, $value, $subject);
        }
        // Gửi email
        $bcc = $CMSNT->site('title');
        sendCSM($getUser['email'], $getUser['username'], $subject, $content, $bcc);
            
    }
 
    
    $servers = $CMSNT->get_list("SELECT * FROM `cron_server` WHERE `status` = 1 ");
    // Truy vấn lấy danh sách các máy chủ mà không cần tính toán trong SQL
    foreach($CMSNT->get_list("SELECT * FROM `cron_server` WHERE `status` = 1 ") as $server){
        // Kiểm tra xem có quá 2 phút (120 giây) từ lần chạy gần nhất không
        if (time() - $server['last_run'] >= 120) {
            // Tạo nội dung tin nhắn thông báo
            $message = "⚠️ [CẢNH BÁO] Mất kết nối đến Máy chủ!

            📅 Thời gian: " . date('Y-m-d H:i:s') . "
            🖥️ Máy chủ: " . $server['name'] . "
            ❌ Trạng thái: Không kết nối được trong " . timeAgo($server['last_run']) . "
            
            Vui lòng kiểm tra và xử lý ngay lập tức.";
            
            // Gửi tin nhắn
            sendMessAdmin($message);
        }
    }
 

    // Thêm các URL của trang web của bạn vào mảng này
    $urls = array();
    $urls[] = base_url('cron-jobs');

    foreach($CMSNT->get_list(" SELECT * FROM posts WHERE `status` = 1 ") as $blog){
        $urls[] = base_url('blog/'.$blog['slug']);
    }
    // Tạo tệp XML mới
    $xml = new DOMDocument('1.0', 'UTF-8');
    $xml->formatOutput = true;
    // Tạo phần tử gốc <urlset> cho sitemap
    $urlset = $xml->createElement('urlset');
    $urlset->setAttribute('xmlns', 'http://www.sitemaps.org/schemas/sitemap/0.9');
    // Thêm các URL vào phần tử gốc <urlset>
    foreach ($urls as $url) {
        $urlElement = $xml->createElement('url');
        $locElement = $xml->createElement('loc', htmlspecialchars($url));
        $urlElement->appendChild($locElement);
        $urlset->appendChild($urlElement);
    }
    $xml->appendChild($urlset);
    // Lưu sitemap vào một tệp
    $xml->save('../sitemap.xml');