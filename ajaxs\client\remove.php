<?php

define("IN_SITE", true);
require_once(__DIR__."/../../config.php");
require_once(__DIR__."/../../libs/db.php");
require_once(__DIR__."/../../libs/lang.php");
require_once(__DIR__."/../../libs/helper.php");
if ($CMSNT->site('status_demo') != 0) {
    $data = json_encode([
        'status'    => 'error',
        'msg'       => __('This function cannot be used because this is a demo site')
    ]);
    die($data);
}
if(!isset($_POST['action'])){
    $data = json_encode([
        'status'    => 'error',
        'msg'       => 'The Request Not Found'
    ]);
    die($data);   
}
if(!isset($_POST['id'])){
    $data = json_encode([
        'status'    => 'error',
        'msg'       => __('The ID to delete does not exist')
    ]);
    die($data);   
}
if ($CMSNT->site('status_demo') != 0) {
    die(json_encode(['status' => 'error', 'msg' => __('Chức năng này không thể sử dụng trên website demo')]));
}
if($_POST['action'] == 'removeMerchant'){
    if (empty($_POST['token'])) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập')]));
    }
    if (!$getUser = $CMSNT->get_row("SELECT * FROM `users` WHERE `token` = '".check_string($_POST['token'])."' AND `banned` = 0 ")) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập')]));
    }
    $id = check_string($_POST['id']);
    if (!$row = $CMSNT->get_row("SELECT * FROM `merchants` WHERE `id` = '$id' AND `user_id` = '".$getUser['id']."' ")) {
        die(json_encode([
            'status'    => 'error',
            'msg'       => __('Cửa hàng này không tồn tại trong hệ thống')
        ]));
    }
    $isRemove = $CMSNT->remove("merchants", " `id` = '".$row['id']."' ");
    if ($isRemove) {
        $Mobile_Detect = new Mobile_Detect();
        $CMSNT->insert("logs", [
            'user_id'       => $getUser['id'],
            'ip'            => myip(),
            'device'        => $Mobile_Detect->getUserAgent(),
            'createdate'    => gettime(),
            'action'        => __('Delete Merchant').' ('.$row['id'].')'
        ]);
        die(json_encode([
            'status'    => 'success',
            'msg'       => __('Xóa cửa hàng thành công!')
        ]));
    }else{
        die(json_encode(['status' => 'error', 'msg' => __('Xóa cửa hàng thất bại!')]));
    }
}

if($_POST['action'] == 'removeFavorite'){
    if (empty($_POST['token'])) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập')]));
    }
    if (!$getUser = $CMSNT->get_row("SELECT * FROM `users` WHERE `token` = '".check_string($_POST['token'])."' AND `banned` = 0 ")) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập')]));
    }
    $id = check_string($_POST['id']);
    if (!$row = $CMSNT->get_row("SELECT * FROM `favorites` WHERE `id` = '$id' AND `user_id` = '".$getUser['id']."' ")) {
        die(json_encode([
            'status'    => 'error',
            'msg'       => __('Xóa dữ liệu thất bại')
        ]));
    }
    $isRemove = $CMSNT->remove("favorites", " `id` = '".$row['id']."' ");
    if ($isRemove) {
        die(json_encode([
            'status'    => 'success',
            'msg'       => __('Xóa dữ liệu thành công')
        ]));
    }
}

// if($_POST['action'] == 'removeInvoice'){
//     if (empty($_POST['token'])) {
//         die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập')]));
//     }
//     if (!$getUser = $CMSNT->get_row("SELECT * FROM `users` WHERE `token` = '".check_string($_POST['token'])."' AND `banned` = 0 ")) {
//         die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập')]));
//     }
//     $id = check_string($_POST['id']);
//     if (!$row = $CMSNT->get_row("SELECT * FROM `invoices` WHERE `id` = '$id' AND `user_id` = '".$getUser['id']."' ")) {
//         $data = json_encode([
//             'status'    => 'error',
//             'msg'       => __('The ID to delete does not exist')
//         ]);
//         die($data);
//     }
//     $isRemove = $CMSNT->remove("invoices", " `id` = '".$row['id']."' ");
//     if ($isRemove) {
//         $Mobile_Detect = new Mobile_Detect();
//         $CMSNT->insert("logs", [
//             'user_id'       => $getUser['id'],
//             'ip'            => myip(),
//             'device'        => $Mobile_Detect->getUserAgent(),
//             'createdate'    => gettime(),
//             'action'        => __('Delete invoice').' ('.$row['value'].')'
//         ]);
//         $data = json_encode([
//             'status'    => 'success',
//             'msg'       => __('Invoice removal successful')
//         ]);
//         die($data);
//     }
// }



die(json_encode([
    'status'    => 'error',
    'msg'       => __('Invalid data')
]));