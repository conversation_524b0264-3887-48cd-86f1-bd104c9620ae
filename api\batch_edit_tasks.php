<?php

/*
 | -------------------------------------------------------------------------
 | batch_edit_tasks.php
 | -------------------------------------------------------------------------
 | Mục đích: X<PERSON> lý cập nhật nhiều CRON chỉ trong 1 request (Batch).
 | Phía client (Python) sẽ gửi POST JSON dạng:
 | {
 |   "tasks": [
 |     {"id": 123, "status": 200, "timeout": 1.23, "output": "OK..."},
 |     {"id": 124, "status": 503, "timeout": 2.5, "output": "Error..."},
 |     ...
 |   ]
 | }
 |
 | Bạn có thể copy và đặt file này trong thư mục /api (chẳng hạn).
 | <PERSON><PERSON><PERSON> bảo đường dẫn, require,... tương tự các file API khác.
*/

define("IN_SITE", true);
require_once(__DIR__."/../libs/db.php");
require_once(__DIR__."/../config.php");
require_once(__DIR__."/../libs/lang.php");
require_once(__DIR__."/../libs/helper.php");

$CMSNT = new DB();

header('Content-Type: application/json');

// Chỉ chấp nhận phương thức POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    die(json_encode(['status' => 'error', 'msg' => __('Method not allowed')]));
}

// Kiểm tra IP hoặc chế độ debug, tương tự edit_task.php
if($CMSNT->site('cronjob_debug') == 1){
    // Nếu debug = 1, có thể cho qua, hoặc chỉ kiểm tra server ở trạng thái "status=1" (tuỳ ý).
    if(!$server = $CMSNT->get_row(" SELECT * FROM `cron_server` WHERE `status` = 1 ")){
        die(json_encode(['status' => 'error', 'msg' => __('Không tìm thấy server cho chế độ debug')]));
    }
}else{
    // Kiểm tra IP
    if(!$server = $CMSNT->get_row(" SELECT * FROM `cron_server` WHERE `ip` = '".myip()."' AND `status` = 1 ")){
        die(json_encode(['status' => 'error', 'msg' => __('Địa chỉ IP không được cấp phép')]));
    }
}

// Đọc dữ liệu JSON từ body
$json_body = file_get_contents('php://input');
$data = json_decode($json_body, true);

// Kiểm tra cấu trúc
if (!isset($data['tasks']) || !is_array($data['tasks'])) {
    die(json_encode(['status' => 'error', 'msg' => __('Dữ liệu không hợp lệ hoặc thiếu "tasks"')]));
}

// Duyệt qua từng CRON trong danh sách
foreach ($data['tasks'] as $task) {
    // Lấy các trường cần thiết
    $id_cron = isset($task['id']) ? intval($task['id']) : 0;
    $status = isset($task['status']) ? $task['status'] : null;
    $timeout = isset($task['timeout']) ? $task['timeout'] : null;
    $output = isset($task['output']) ? $task['output'] : null;
    $run_at = isset($task['run_at']) ? $task['run_at'] :gettime();

    // Kiểm tra id_cron
    if ($id_cron <= 0) {
        // Bỏ qua hoặc tiếp tục
        continue;
    }

    // Tìm CRON trong DB
    $cron = $CMSNT->get_row("SELECT * FROM `cron_jobs` WHERE `id` = '$id_cron' ");
    if (!$cron) {
        // Không tồn tại CRON này => tiếp tục xử lý CRON kế tiếp
        continue;
    }

    // Nếu output dài hơn 100 ký tự, cắt bớt (giống logic edit_task.php)
    if ($output && mb_strlen($output) > 100) {
        $output = mb_substr($output, 0, 100) . '...';
    }

    // Cập nhật cron_jobs
    $isUpdate = $CMSNT->update('cron_jobs', [
        'last_run'  => time(),
        'output'    => $output,
        'code'      => $status,
        'timeout'   => $timeout
    ], " `id` = '".$cron['id']."' ");

    // Nếu update thành công, ghi log vào cron_history
    if ($isUpdate) {
        // Thêm vào cron_history
        $CMSNT->insert('cron_history', [
            'cron_job_id'   => $cron['id'],
            'server_id'     => $server['id'],
            'run_at'        => $run_at,
            'code'          => $status,
            'output'        => $output,
            'timeout'       => $timeout
        ]);

        // Xoá lịch sử cũ, chỉ giữ 100 bản ghi
        $CMSNT->query("
            DELETE FROM `cron_history` 
            WHERE `cron_job_id` = '".$cron['id']."'
            AND `run_at` NOT IN (
                SELECT `run_at` FROM (
                    SELECT `run_at`
                    FROM `cron_history`
                    WHERE `cron_job_id` = '".$cron['id']."'
                    ORDER BY `run_at` DESC
                    LIMIT 100
                ) AS tmp
            )
        ");
    }
}

// Trả về kết quả
die(json_encode(['status' => 'success', 'msg' => __('Batch update thành công')]));

