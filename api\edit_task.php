<?php

define("IN_SITE", true);
require_once(__DIR__."/../libs/db.php");
require_once(__DIR__."/../config.php");
require_once(__DIR__."/../libs/lang.php");
require_once(__DIR__."/../libs/helper.php");
$CMSNT = new DB();



if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if($CMSNT->site('cronjob_debug') == 1){
        if(!$server = $CMSNT->get_row(" SELECT * FROM `cron_server` WHERE `status` = 1 ")){
            die(json_encode(['status' => 'error', 'msg' => __('Địa chỉ IP không được cấp phép')]));
        }
    }else{
        if(!$server = $CMSNT->get_row(" SELECT * FROM `cron_server` WHERE `ip` = '".myip()."' AND `status` = 1 ")){
            die(json_encode(['status' => 'error', 'msg' => __('Địa chỉ IP không được cấp phép')]));
        }
    }
    if(empty($_POST['id'])){
        die(json_encode(['status' => 'error', 'msg' => __('Thiếu id')]));
    }
    if(!$cron = $CMSNT->get_row(" SELECT * FROM `cron_jobs` WHERE `id` = '".check_string($_POST['id'])."' ")){
        die(json_encode(['status' => 'error', 'msg' => __('id không tồn tại trong hệ thống')]));
    }
    $output = !empty($_POST['output']) ? check_string($_POST['output']) : NULL;
    // Kiểm tra nếu $output có giá trị và độ dài lớn hơn 1000 ký tự
    if ($output !== NULL && strlen($output) > 100) {
        $output = substr($output, 0, 100).'...'; // Chỉ lấy 1000 ký tự đầu tiên
    }
    $code = !empty($_POST['status']) ? check_string($_POST['status']) : NULL; // error code
    $timeout = !empty($_POST['timeout']) ? check_string($_POST['timeout']) : NULL; // timeout

    $isUpdate = $CMSNT->update('cron_jobs', [
        'last_run'  => time(),
        'output'    => $output,
        'code'      => $code,
        'timeout'   => $timeout
    ], " `id` = '".$cron['id']."' ");
    if($isUpdate){
        // Xóa lịch sử cũ hơn 100 bản ghi
        $CMSNT->query("DELETE FROM `cron_history` WHERE `cron_job_id` = '".$cron['id']."' AND `run_at` NOT IN (SELECT `run_at` FROM (SELECT `run_at` FROM `cron_history` WHERE `cron_job_id` = '".$cron['id']."' ORDER BY `run_at` DESC LIMIT 100) AS tmp)");

        $CMSNT->insert('cron_history', [
            'cron_job_id'   => $cron['id'],
            'server_id'     => $server['id'],
            'run_at'        => gettime(),
            'code'          => $code,
            'output'        => $output,
            'timeout'       => $timeout
        ]);
        die(json_encode(['status' => 'success', 'msg' => __('Cập nhật thành công')]));
    }
}