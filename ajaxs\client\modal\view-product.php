<?php

define("IN_SITE", true);
require_once(__DIR__."/../../../config.php");
require_once(__DIR__."/../../../libs/db.php");
require_once(__DIR__."/../../../libs/lang.php");
require_once(__DIR__."/../../../libs/helper.php");

if (isSecureCookie('user_login') == true) {
    require_once(__DIR__ . '/../../models/is_user.php');
}
 
if(!$product = $CMSNT->get_row(" SELECT * FROM `products` WHERE `id` = '".check_string($_GET['id'])."' AND `status` = 1 ")){
    die('<script type="text/javascript">if(!alert("'.__('Sản phảm không tồn tại').'")){location.reload();}</script>');
}
$stock = $product['supplier_id'] != 0 ? $product['api_stock'] : getStock($product['code']);
?>
<button class="modal-close icofont-close" data-bs-dismiss="modal"></button>
<div class="product-view">
    <div class="row">
        <div class="col-md-6 col-lg-6">
            <div class="view-details">
                <h3 class="view-name"><a
                        href="<?=base_url('product/'.$product['slug']);?>"><?=__($product['name']);?></a></h3>
                <div class="view-meta">
                    <p><label class="label-text feat"><?=__('Kho hàng:');?>
                            <strong><?=format_cash($stock);?></strong></label>
                        <?php if($CMSNT->site('product_sold_display') == 1):?>
                        <label class="label-text order"><?=__('Đã bán:');?>
                            <strong><?=format_cash($product['sold']);?></strong></label>
                        <?php endif?>
                    </p>
                </div>
                <?php if($CMSNT->site('product_rating_display') == 1):?>
                <div class="view-rating">
                    <i class="active icofont-star"></i>
                    <i class="active icofont-star"></i>
                    <i class="active icofont-star"></i>
                    <i class="active icofont-star"></i><i class="icofont-star"></i>
                    <a href="product-video.html">(3 reviews)</a>
                </div>
                <?php endif?>

                <h3 class="view-price">
                    <?=$product['discount'] > 0 ? '<del>'.format_currency($product['price']).'</del>' : '';?><span><?=format_currency($product['price']-$product['price']*$product['discount']/100);?></span>
                </h3>
                <?php // KO ÁP DỤNG CHO USER ĐÃ ĐƯỢC CHIẾT KHẤU RIÊNG
                    if(!isset($getUser) || $getUser['discount'] == 0):?>
                <?php if($CMSNT->num_rows(" SELECT * FROM product_discount WHERE product_id = '".$product['id']."' ") > 0):?>
                <div class="mb-3 card-hot-deal">
                    <span><i class="fa-solid fa-fire-flame-simple" style="color:red;"></i> Hot Deal: </span><br>
                    <?php foreach($CMSNT->get_list(" SELECT * FROM product_discount WHERE product_id = '".$product['id']."' ") as $product_discount):?>
                    <span> * <?=__('Mua');?> >= <b style="color:blue;"><?=format_cash($product_discount['min']);?></b>
                        <?=__('tài khoản giảm');?> <b style="color:red;"><?=$product_discount['discount'];?>%</b></span>
                    <br>
                    <?php endforeach?>
                </div>
                <?php endif?>
                <?php endif?>
                <p class="view-desc"><?=str_replace(PHP_EOL, '<br>', $product['short_desc']);?></p>
                <div class="view-list-group">
                    <label class="view-list-title"><?=__('Chia sẻ:');?></label>
                    <ul class="view-share-list">
                        <li><a href="https://www.facebook.com/sharer/sharer.php?u=<?=base_url('product/'.$product['slug']);?>"
                                title="Facebook"><i class="fa-brands fa-facebook"></i></a></li>
                        <li><a href="https://twitter.com/intent/tweet?url=<?=base_url('product/'.$product['slug']);?>"
                                title="Twitter"><i class="fa-brands fa-square-x-twitter"></i></a></li>
                        <li><a href="https://www.linkedin.com/sharing/share-offsite/?url=<?=base_url('product/'.$product['slug']);?>"
                                title="Linkedin"><i class="fa-brands fa-linkedin"></i></a></li>
                        <li><a href="https://www.instagram.com/?url=<?=base_url('product/'.$product['slug']);?>"
                                title="Instagram"><i class="fa-brands fa-instagram"></i></a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-6">
            <div class="view-details">
                <table class="table fs-sm mb-0">
                    <tbody>
                        <tr>
                            <td colspan="2" align="center"><strong><?=__('THÔNG TIN MUA HÀNG');?></strong></td>
                        </tr>
                        <tr>
                        <tr>
                            <td><?=__('Số dư của tôi:');?></td>
                            <td class="text-right"><strong
                                    class="text-wallet"><?=isset($getUser) ? format_currency($getUser['money']) : 0;?></strong>
                            </td>
                        </tr>
                        <td><?=__('Số lượng cần mua:');?> (<span class="text-danger">*</span>)</td>
                        <td>
                            <div class="product-action" style="display: flex;">
                                <input type="hidden" id="product_id" value="<?=$product['id'];?>">
                                <input type="hidden" id="api_key" value="<?=isset($getUser) ? $getUser['api_key'] : ''?>">
                                <input type="hidden" id="token" value="<?=isset($getUser) ? $getUser['token'] : ''?>">
                                <button class="action-minus1" title="Quantity Minus"><i
                                        class="fa-solid fa-minus"></i></button>
                                <input class="action-input" onkeyup="totalPayment()" title="Quantity Number" type="number"
                                    id="amount" value="1">
                                <button class="action-plus1" title="Quantity Plus"><i
                                        class="fa-solid fa-plus"></i></button>
                            </div>
                        </td>
                        </tr>
                        <tr>
                            <td><?=__('Mã giảm giá:');?></td>
                            <td><input class="form-control-view-product" onchange="totalPayment()" type="text"
                                    id="coupon" placeholder="<?=__('Nhập mã giảm giá nếu có');?>"></td>
                        </tr>
                        <tr>
                            <td><?=__('Thành tiền:');?></td>
                            <td class="text-right"><strong id="into_money">0</strong></td>
                        </tr>
                        <tr>
                            <td><?=__('Số tiền giảm:');?></td>
                            <td class="text-right"><strong style="color: red;" id="into_discount">0</strong></td>
                        </tr>
                        <tr>
                            <td><?=__('Tổng tiền thanh toán:');?></td>
                            <td class="text-right"><strong style="color: blue;" id="into_pay">0</strong></td>
                        </tr>
                    </tbody>
                </table>
                <div class="view-add-group">
                    <button class="btn-buy" id="btnBuy" onclick="buyProduct()">
                        <i class="fa-solid fa-cart-shopping"></i>
                        <span><?=__('THANH TOÁN');?></span>
                    </button>
                </div>
                <div class="view-action-group">
                    <?php 
                    $isButtonFavorite = false;
                    if(isset($getUser['id'])){
                        $isButtonFavorite = $CMSNT->get_row(" SELECT * FROM `favorites` WHERE `user_id` = '".$getUser['id']."' AND `product_id` = '".$product['id']."' ");
                    }
                    ?>
                    <input type="checkbox" <?=$isButtonFavorite == true ? 'checked="checked"' : '';?>
                        onclick="addFavorite()" id="favorite" class="input_favorite" name="favorite-checkbox"
                        value="favorite-button">
                    <label for="favorite" class="label_favorite">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-heart">
                            <path
                                d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z">
                            </path>
                        </svg>
                        <div class="action">
                            <span class="option-1"><?=__('Thêm vào mục yêu thích');?></span>
                            <span class="option-2"><?=__('Đã thêm vào mục yêu thích');?></span>
                        </div>
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>



<script>
function buyProduct() {
    $('#btnBuy').html('<i class="fa fa-spinner fa-spin"></i> <?=__('Đang xử lý...');?>').prop(
        'disabled',
        true);
    $.ajax({
        url: "<?=BASE_URL("ajaxs/client/product.php");?>",
        method: "POST",
        dataType: "JSON",
        data: {
            action: 'buyProduct',
            id: $("#product_id").val(),
            amount: $("#amount").val(),
            coupon: $("#coupon").val(),
            api_key: $("#api_key").val()
        },
        success: function(result) {
            if (result.status == 'success') {
                Swal.fire({
                    icon: 'success',
                    title: '<?=__('Thành công !');?>',
                    text: result.msg,
                    showDenyButton: true,
                    confirmButtonText: '<?=__('Mua thêm');?>',
                    denyButtonText: `<?=__('Xem chi tiết đơn hàng');?>`,
                }).then((result) => {
                    if (result.isConfirmed) {
                        location.reload();
                    } else if (result.isDenied) {
                        window.location.href =
                            '<?=base_url('product-order/');?>' + result.trans_id;
                    }
                });
            } else {
                Swal.fire('<?=__('Thất bại!');?>', result.msg, 'error');
            }
            $('#btnBuy').html(
                '<i class="fa-solid fa-cart-shopping"></i> <span><?=__('THANH TOÁN');?></span>').prop(
                'disabled',
                false);
        },
        error: function() {
            showMessage('<?=__('Vui lòng liên hệ Developer');?>', 'error');
        }
    });
}
</script>
<script>
function totalPayment() {
    const product_id = $("#product_id").val();
    const amount = $("#amount").val();
    const coupon = $("#coupon").val();
    const token = $("#token").val();
    $.ajax({
        url: "<?=BASE_URL('ajaxs/client/product.php');?>",
        method: "POST",
        dataType: "JSON",
        data: {
            action: 'total_payment',
            id: product_id,
            amount: amount,
            coupon: coupon,
            token: token
        },
        success: function(data) {
            if (data.status == 'success') {
                const into_money = $("#into_money");
                const into_discount = $("#into_discount");
                const into_pay = $("#into_pay");
                into_money.html(data.money);
                into_discount.html(data.discount);
                into_pay.html(data.pay);
                if (data.discount_number != 0) {
                    showMessage('<?=__('Áp dụng giảm giá thành công!');?>', 'success');
                }
            } else {
                showMessage(data.msg, data.status);
            }
        },
        error: function() {
            showMessage('<?=__('Vui lòng liên hệ Developer');?>', 'error');
        }
    });
}
totalPayment();
</script>

<script>
const inputElement = document.querySelector('#amount');
const plusButton = document.querySelector('.action-plus1');
const minusButton = document.querySelector('.action-minus1');
plusButton.addEventListener('click', function() {
    let currentValue = parseInt(inputElement.value);
    currentValue++;
    inputElement.value = currentValue;
    totalPayment();
});
minusButton.addEventListener('click', function() {
    let currentValue = parseInt(inputElement.value);
    currentValue = Math.max(1, currentValue - 1);
    inputElement.value = currentValue;
    totalPayment();
});
</script>

<script>
function addFavorite() {
    $.ajax({
        url: "<?=BASE_URL("ajaxs/client/update.php");?>",
        method: "POST",
        dataType: "JSON",
        data: {
            action: 'toggleFavorite',
            id: $("#product_id").val(),
            token: $("#token").val()
        },
        success: function(data) {
            if (data.status == 'success') {
                if (data.button == true) {
                    $("#btnAddFavorite").hide();
                    $("#btnRemoveFavorite").show();
                    // Lấy thẻ sup theo id
                    var numFavoritesElement = document.getElementById("numFavorites");
                    // Giá trị hiện tại trong thẻ sup
                    var currentValue = parseInt(numFavoritesElement.textContent);
                    // Giả sử bạn muốn cộng thêm 1 vào giá trị hiện tại
                    var newValue = currentValue + 1;
                    // Cập nhật giá trị trong thẻ sup
                    numFavoritesElement.textContent = newValue;
                } else {
                    $("#btnAddFavorite").show();
                    $("#btnRemoveFavorite").hide();
                    // Lấy thẻ sup theo id
                    var numFavoritesElement = document.getElementById("numFavorites");
                    // Giá trị hiện tại trong thẻ sup
                    var currentValue = parseInt(numFavoritesElement.textContent);
                    // Giả sử bạn muốn cộng thêm 1 vào giá trị hiện tại
                    var newValue = currentValue - 1;
                    // Cập nhật giá trị trong thẻ sup
                    numFavoritesElement.textContent = newValue;
                }
            }else{
                showMessage(data.msg, 'error');
            }
            
        },
        error: function() {
            showMessage('<?=__('Vui lòng liên hệ Developer');?>', 'error');
        }
    });
}
</script>