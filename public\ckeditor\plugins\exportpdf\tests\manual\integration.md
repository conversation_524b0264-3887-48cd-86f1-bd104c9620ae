@bender-tags: exportpdf, feature, 17
@bender-ui: collapsed
@bender-include: ../_helpers/tools.js
@bender-ckeditor-plugins: basicstyles, bidi, blockquote, clipboard, colorbutton, colordialog, dialogadvtab, elementspath, enterkey, font, format, horizontalrule, htmlwriter, image, indentlist, indentblock, justify, link, list, liststyle, magicline, pagebreak, pastefromgdocs, pastefromlibreoffice, pastefromword, pastetext, specialchar, stylescombo, table, tableselection, tabletools, toolbar, undo, wysiwygarea, sourcearea, resize

1. Click `Export to PDF` button (the one next to the `Source` button).

1. Wait for the PDF to download and examine it.

If you find any bugs, please report them <a href="https://github.com/cksource/ckeditor4-plugin-exportpdf/issues" target="_blank">here</a>. Just remember to check if it isn't <a href="https://github.com/cksource/ckeditor4-plugin-exportpdf/issues?q=is%3Aopen+is%3Aissue+label%3Atype%3Abug" target="_blank">already known</a>.

When comparing generated PDF to editor contents the best conversion results (close to 1:1 document formatting) can be observed in a Chrome browser.
