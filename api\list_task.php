<?php

define("IN_SITE", true);
require_once(__DIR__."/../libs/db.php");
require_once(__DIR__."/../config.php");
require_once(__DIR__."/../libs/lang.php");
require_once(__DIR__."/../libs/helper.php");
$CMSNT = new DB();



if ($_SERVER['REQUEST_METHOD'] == 'GET') {
  
    if($CMSNT->site('cronjob_debug') == 1){
        if(!$cron_server = $CMSNT->get_row(" SELECT * FROM `cron_server` WHERE `status` = 1 ")){
            die(json_encode(['status' => 'error', 'msg' => __('Địa chỉ IP không được cấp phép')]));
        }
    }else{
        if(!$cron_server = $CMSNT->get_row(" SELECT * FROM `cron_server` WHERE `ip` = '".myip()."' AND `status` = 1 ")){
            die(json_encode(['status' => 'error', 'msg' => __('Địa chỉ IP '.myip().' không được cấp phép')]));
        }
    }

    $CMSNT->update('cron_server', [
        'last_run'  => time()
    ], " `id` = '".$cron_server['id']."' ");


    $list_cron = [];
    $current_time = time();
    $sql = "SELECT * FROM `cron_jobs` 
            WHERE `status` = 'active' 
            AND `server_id` = '".$cron_server['id']."' 
            AND ($current_time - `last_run`) >= `timeloop`
            ORDER BY RAND() ";

    foreach($CMSNT->get_list($sql) as $cron) {

        // Tính toán khoảng thời gian kể từ lần chạy gần nhất
        $currentTime = time();
        $last = $currentTime - intval($cron['last_run']); // Khoảng thời gian đã trôi qua từ lần chạy cuối
        // Tính toán xem đã vượt quá vòng lặp cộng thêm Timeout chưa
        if ($last > intval($cron['timeloop']) + intval($cron['timeout']) + 3) {
            $CMSNT->update('cron_jobs', [
                'last_run' => $currentTime
            ], " `id` = '" . intval($cron['id']) . "' ");
        }
        
        $list_cron[] = [
            'id'            => intval($cron['id']),
            'link_cron'     => $cron['link_cron'],
            'method'        => $cron['method'],
            'timeloop'      => intval($cron['timeloop']), // Vòng lặp user chọn
            'last_run'      => intval($cron['last_run']), // Thời gian chạy gần nhất
            'timeout'       => intval($cron_server['timeout']) // Timeout tối đa Server cho phép
        ];
    }

    die(json_encode([
        'status' => 'success', 
        'msg' => __('Lấy dữ liệu CRON thành công'),
        'data'  => $list_cron
    ]));

}