<!DOCTYPE html>
<!--
Copyright (c) 2003-2021, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
-->
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>Using API to Customize Dialog Windows &mdash; CKEditor Sample</title>
	<script src="../../../ckeditor.js"></script>
	<link rel="stylesheet" href="../../../samples/old/sample.css">
	<meta name="ckeditor-sample-name" content="Using the JavaScript API to customize dialog windows">
	<meta name="ckeditor-sample-group" content="Advanced Samples">
	<meta name="ckeditor-sample-description" content="Using the dialog windows API to customize dialog windows without changing the original editor code.">
	<meta name="description" content="Try the latest sample of CKEditor 4 and learn more about customizing your WYSIWYG editor with endless possibilities.">
	<style>

		.cke_button__mybutton_icon
		{
			display: none !important;
		}

		.cke_button__mybutton_label
		{
			display: inline !important;
		}

	</style>
	<script>

		CKEDITOR.on( 'instanceCreated', function( ev ){
			var editor = ev.editor;

			// Listen for the "pluginsLoaded" event, so we are sure that the
			// "dialog" plugin has been loaded and we are able to do our
			// customizations.
			editor.on( 'pluginsLoaded', function() {

				// If our custom dialog has not been registered, do that now.
				if ( !CKEDITOR.dialog.exists( 'myDialog' ) ) {
					// We need to do the following trick to find out the dialog
					// definition file URL path. In the real world, you would simply
					// point to an absolute path directly, like "/mydir/mydialog.js".
					var href = document.location.href.split( '/' );
					href.pop();
					href.push( 'assets/my_dialog.js' );
					href = href.join( '/' );

					// Finally, register the dialog.
					CKEDITOR.dialog.add( 'myDialog', href );
				}

				// Register the command used to open the dialog.
				editor.addCommand( 'myDialogCmd', new CKEDITOR.dialogCommand( 'myDialog' ) );

				// Add the a custom toolbar buttons, which fires the above
				// command..
				editor.ui.add( 'MyButton', CKEDITOR.UI_BUTTON, {
					label: 'My Dialog',
					command: 'myDialogCmd'
				});
			});
		});

		// When opening a dialog, its "definition" is created for it, for
		// each editor instance. The "dialogDefinition" event is then
		// fired. We should use this event to make customizations to the
		// definition of existing dialogs.
		CKEDITOR.on( 'dialogDefinition', function( ev ) {
			// Take the dialog name and its definition from the event data.
			var dialogName = ev.data.name;
			var dialogDefinition = ev.data.definition;

			// Check if the definition is from the dialog we're
			// interested on (the "Link" dialog).
			if ( dialogName == 'myDialog' && ev.editor.name == 'editor2' ) {
				// Get a reference to the "Link Info" tab.
				var infoTab = dialogDefinition.getContents( 'tab1' );

				// Add a new text field to the "tab1" tab page.
				infoTab.add( {
					type: 'text',
					label: 'My Custom Field',
					id: 'customField',
					'default': 'Sample!',
					validate: function() {
						if ( ( /\d/ ).test( this.getValue() ) )
							return 'My Custom Field must not contain digits';
					}
				});

				// Remove the "select1" field from the "tab1" tab.
				infoTab.remove( 'select1' );

				// Set the default value for "input1" field.
				var input1 = infoTab.get( 'input1' );
				input1[ 'default' ] = 'www.example.com';

				// Remove the "tab2" tab page.
				dialogDefinition.removeContents( 'tab2' );

				// Add a new tab to the "Link" dialog.
				dialogDefinition.addContents( {
					id: 'customTab',
					label: 'My Tab',
					accessKey: 'M',
					elements: [
						{
							id: 'myField1',
							type: 'text',
							label: 'My Text Field'
						},
						{
							id: 'myField2',
							type: 'text',
							label: 'Another Text Field'
						}
					]
				});

				// Provide the focus handler to start initial focus in "customField" field.
				dialogDefinition.onFocus = function() {
					var urlField = this.getContentElement( 'tab1', 'customField' );
					urlField.select();
				};
			}
		});

		var config = {
			extraPlugins: 'dialog',
			toolbar: [ [ 'MyButton' ] ]
		};

	</script>
</head>
<body>
	<h1 class="samples">
		<a href="../../../samples/old/index.html">CKEditor Samples</a> &raquo; Using CKEditor Dialog API
	</h1>
	<div class="warning deprecated">
		This sample is not maintained anymore. Check out the <a href="https://ckeditor.com/docs/ckeditor4/latest/examples/index.html">brand new samples in CKEditor Examples</a>.
	</div>
	<div class="description">
		<p>
			This sample shows how to use the
			<a class="samples" href="https://ckeditor.com/docs/ckeditor4/latest/api/CKEDITOR_dialog.html">CKEditor Dialog API</a>
			to customize CKEditor dialog windows without changing the original editor code.
			The following customizations are being done in the example below:
		</p>
		<p>
			For details on how to create this setup check the source code of this sample page.
		</p>
	</div>
	<p>A custom dialog is added to the editors using the <code>pluginsLoaded</code> event, from an external <a target="_blank" href="assets/my_dialog.js">dialog definition file</a>:</p>
	<ol>
		<li><strong>Creating a custom dialog window</strong> &ndash; "My Dialog" dialog window opened with the "My Dialog" toolbar button.</li>
		<li><strong>Creating a custom button</strong> &ndash; Add button to open the dialog with "My Dialog" toolbar button.</li>
	</ol>
	<textarea cols="80" id="editor1" name="editor1" rows="10">&lt;p&gt;This is some &lt;strong&gt;sample text&lt;/strong&gt;. You are using &lt;a href="https://ckeditor.com/"&gt;CKEditor&lt;/a&gt;.&lt;/p&gt;</textarea>
	<script>
		// Replace the <textarea id="editor1"> with an CKEditor instance.
		CKEDITOR.replace( 'editor1', config );
	</script>
	<p>The below editor modify the dialog definition of the above added dialog using the <code>dialogDefinition</code> event:</p>
	<ol>
		<li><strong>Adding dialog tab</strong> &ndash; Add new tab "My Tab" to dialog window.</li>
		<li><strong>Removing a dialog window tab</strong> &ndash; Remove "Second Tab" page from the dialog window.</li>
		<li><strong>Adding dialog window fields</strong> &ndash; Add "My Custom Field" to the dialog window.</li>
		<li><strong>Removing dialog window field</strong> &ndash; Remove "Select Field" selection field from the dialog window.</li>
		<li><strong>Setting default values for dialog window fields</strong> &ndash; Set default value of "Text Field" text field. </li>
		<li><strong>Setup initial focus for dialog window</strong> &ndash; Put initial focus on "My Custom Field" text field. </li>
	</ol>
	<textarea cols="80" id="editor2" name="editor2" rows="10">&lt;p&gt;This is some &lt;strong&gt;sample text&lt;/strong&gt;. You are using &lt;a href="https://ckeditor.com/"&gt;CKEditor&lt;/a&gt;.&lt;/p&gt;</textarea>
	<script>

		// Replace the <textarea id="editor1"> with an CKEditor instance.
		CKEDITOR.replace( 'editor2', config );

	</script>
	<div id="footer">
		<hr>
		<p>
			CKEditor - The text editor for the Internet - <a class="samples" href="https://ckeditor.com/">https://ckeditor.com</a>
		</p>
		<p id="copy">
			Copyright &copy; 2003-2021, <a class="samples" href="https://cksource.com/">CKSource</a> - Frederico
			Knabben. All rights reserved.
		</p>
	</div>
</body>
</html>
