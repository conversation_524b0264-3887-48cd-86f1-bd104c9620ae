<?php

define("IN_SITE", true);
require_once(__DIR__."/../../../config.php");
require_once(__DIR__."/../../../libs/db.php");
require_once(__DIR__."/../../../libs/lang.php");
require_once(__DIR__."/../../../libs/helper.php");

if (empty($_POST['token'])) {
    die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')]));
}
if (!$getUser = $CMSNT->get_row("SELECT * FROM users WHERE token = '".check_string($_POST['token'])."' AND banned = 0 ")) {
    die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')]));
}
if(!$cron = $CMSNT->get_row(" SELECT * FROM cron_jobs WHERE id = '".check_string($_POST['id'])."' AND user_id = '".$getUser['id']."' ")){
    die(json_encode(['status' => 'error', 'msg' => __('Dữ liệu không hợp lệ')]));
}

// Lấy dữ liệu từ cron_history
$history = $CMSNT->get_list("SELECT * FROM cron_history WHERE cron_job_id = '".$cron['id']."' ORDER BY id DESC");

$response = [
    'status' => 'success',
    'cron' => [
        'link' => $cron['link_cron']
    ],
    'history' => []
];

// Chuẩn bị dữ liệu JSON
foreach ($history as $row) {
    $response['history'][] = [
        'run_at' => $row['run_at'],
        'time_ago' => timeAgo(strtotime($row['run_at'])),
        'status' => display_cron_jobs($row['code']),
        'timeout' => $row['timeout']
    ];
}

// Trả về JSON
echo json_encode($response);
?>
