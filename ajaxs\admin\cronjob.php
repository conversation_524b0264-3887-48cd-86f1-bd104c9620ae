<?php

define("IN_SITE", true);
require_once(__DIR__."/../../config.php");
require_once(__DIR__."/../../libs/db.php");
require_once(__DIR__."/../../libs/lang.php");
require_once(__DIR__."/../../libs/helper.php");
require_once(__DIR__.'/../../models/is_admin.php');
if ($CMSNT->site('status_demo') != 0) {
    $data = json_encode([
        'status' => 'error',
        'msg' => __('This function cannot be used because this is a demo site')
    ]);
    die($data);
}

if (!isset($_POST['action'])) {
    die(json_encode([
        'status' => 'error',
        'msg' => 'The Request Not Found'
    ]));
}

if ($_POST['action'] == 'activeCron') {
    if (checkPermission($getUser['admin'], 'edit_cron') != true) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Bạn không có quyền sử dụng tính năng này')
        ]));
    }
    if (empty($_POST['id'])) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('ID không tồn tại trong hệ thống')
        ]));
    }
    if (empty($_POST['token'])) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')
        ]));
    }
    if (!$getUser = $CMSNT->get_row("SELECT * FROM `users` WHERE `token` = '" . check_string($_POST['token']) . "' AND `banned` = 0 AND `admin` != 0 ")) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')
        ]));
    }
    $id = check_string($_POST['id']);
    if (!$cron = $CMSNT->get_row("SELECT * FROM `cron_jobs` WHERE `id` = '$id' ")) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Dữ liệu không tại trong hệ thống')
        ]));
    }
    $isUpdate = $CMSNT->update('cron_jobs', [
        'status' => 'active',
        'updated_at' => gettime()
    ], " `id` = '" . $cron['id'] . "' ");
    if ($isUpdate) {
        $Mobile_Detect = new Mobile_Detect();
        $CMSNT->insert("logs", [
            'user_id' => $getUser['id'],
            'ip' => myip(),
            'device' => $Mobile_Detect->getUserAgent(),
            'createdate' => gettime(),
            'action' => __('Kích hoạt lại CRON') . ' (' . $cron['link_cron'] . ')'
        ]);
        die(json_encode([
            'status' => 'success',
            'msg' => __('Kích hoạt thành công!')
        ]));
    } else {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Kích hoạt thất bại')
        ]));
    }
}

if ($_POST['action'] == 'pauseCron') {
    if (checkPermission($getUser['admin'], 'edit_cron') != true) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Bạn không có quyền sử dụng tính năng này')
        ]));
    }
    if (empty($_POST['id'])) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('ID không tồn tại trong hệ thống')
        ]));
    }
    if (empty($_POST['token'])) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')
        ]));
    }
    if (!$getUser = $CMSNT->get_row("SELECT * FROM `users` WHERE `token` = '" . check_string($_POST['token']) . "' AND `banned` = 0 AND `admin` != 0 ")) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')
        ]));
    }
    $id = check_string($_POST['id']);
    if (!$cron = $CMSNT->get_row("SELECT * FROM `cron_jobs` WHERE `id` = '$id' ")) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Dữ liệu không tại trong hệ thống')
        ]));
    }
    $isUpdate = $CMSNT->update('cron_jobs', [
        'status' => 'pause',
        'updated_at' => gettime()
    ], " `id` = '" . $cron['id'] . "' ");
    if ($isUpdate) {
        $Mobile_Detect = new Mobile_Detect();
        $CMSNT->insert("logs", [
            'user_id' => $getUser['id'],
            'ip' => myip(),
            'device' => $Mobile_Detect->getUserAgent(),
            'createdate' => gettime(),
            'action' => __('Tạm dừng CRON') . ' (' . $cron['link_cron'] . ')'
        ]);
        die(json_encode([
            'status' => 'success',
            'msg' => __('Dừng chạy thành công!')
        ]));
    } else {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Dừng chạy thất bại')
        ]));
    }
}

if ($_POST['action'] == 'HistoryCron') {
    if (checkPermission($getUser['admin'], 'view_cron') != true) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Bạn không có quyền sử dụng tính năng này')
        ]));
    }
    if (empty($_POST['token'])) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')
        ]));
    }
    if (!$getUser = $CMSNT->get_row("SELECT * FROM users WHERE token = '" . check_string($_POST['token']) . "' AND banned = 0 AND `admin` != 0 ")) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')
        ]));
    }
    if (!$cron = $CMSNT->get_row(" SELECT * FROM cron_jobs WHERE id = '" . check_string($_POST['id']) . "' ")) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Dữ liệu không hợp lệ')
        ]));
    }
    // Lấy dữ liệu từ cron_history
    $history = $CMSNT->get_list("SELECT * FROM cron_history WHERE cron_job_id = '" . $cron['id'] . "' ORDER BY id DESC");
    $response = [
        'status' => 'success',
        'cron' => [
            'link' => $cron['link_cron']
        ],
        'history' => []
    ];
    // Chuẩn bị dữ liệu JSON
    foreach ($history as $row) {
        $response['history'][] = [
            'run_at' => $row['run_at'],
            'time_ago' => timeAgo(strtotime($row['run_at'])),
            'status' => display_cron_jobs($row['code']),
            'timeout' => $row['timeout'],
            'output' => $row['output']
        ];
    }
    // Trả về JSON
    die(json_encode($response));
}


if ($_POST['action'] == 'deleteCron') {
    if (checkPermission($getUser['admin'], 'edit_cron') != true) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Bạn không có quyền sử dụng tính năng này')
        ]));
    }
    if (empty($_POST['id'])) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('ID không tồn tại trong hệ thống')
        ]));
    }
    if (empty($_POST['token'])) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')
        ]));
    }
    if (!$getUser = $CMSNT->get_row("SELECT * FROM `users` WHERE `token` = '" . check_string($_POST['token']) . "' AND `banned` = 0 AND `admin` != 0 ")) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')
        ]));
    }
    $id = check_string($_POST['id']);
    if (!$cron = $CMSNT->get_row("SELECT * FROM `cron_jobs` WHERE `id` = '$id' ")) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Dữ liệu không tại trong hệ thống')
        ]));
    }
    $isRemove = $CMSNT->remove('cron_jobs', " `id` = '" . $cron['id'] . "' ");
    if ($isRemove) {
        // Xóa History
        $CMSNT->remove('cron_history', " `cron_job_id` = '".$cron['id']."' ");
        $Mobile_Detect = new Mobile_Detect();
        $CMSNT->insert("logs", [
            'user_id' => $getUser['id'],
            'ip' => myip(),
            'device' => $Mobile_Detect->getUserAgent(),
            'createdate' => gettime(),
            'action' => __('Xóa link CRON') . ' (' . $cron['link_cron'] . ')'
        ]);
        die(json_encode([
            'status' => 'success',
            'msg' => __('Xóa link thành công!')
        ]));
    } else {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Xóa link thất bại')
        ]));
    }
}


die(json_encode([
    'status' => 'error',
    'msg' => __('Invalid data')
]));
