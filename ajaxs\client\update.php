<?php

use GuzzleHttp\Promise\Is;

define("IN_SITE", true);
require_once(__DIR__."/../../config.php");
require_once(__DIR__."/../../libs/db.php");
require_once(__DIR__."/../../libs/lang.php");
require_once(__DIR__."/../../libs/helper.php");
require_once(__DIR__.'/../../libs/database/users.php');

if(!isset($_POST['action'])){
    $data = json_encode([
        'status'    => 'error',
        'msg'       => __('The Request Not Found')
    ]);
    die($data);   
}

 

if($_POST['action'] == 'changeLanguage'){
    if (empty($_POST['id'])) {
        die(json_encode(['status' => 'error', 'msg' => __('Data does not exist')]));
    }
    $id = check_string($_POST['id']);
    $row = $CMSNT->get_row("SELECT * FROM `languages` WHERE `id` = '$id' ");
    if (!$row) {
        die(json_encode(['status' => 'error', 'msg' => __('Data does not exist')]));
    }
    $isUpdate = setLanguage($id);
    if ($isUpdate) {
        $data = json_encode([
            'status'    => 'success',
            'msg'       => __('Change language successfully')
        ]);
        die($data);
    }
}

if($_POST['action'] == 'changeCurrency'){
    if (empty($_POST['id'])) {
        die(json_encode(['status' => 'error', 'msg' => __('Data does not exist')]));
    }
    $id = check_string($_POST['id']);
    $row = $CMSNT->get_row("SELECT * FROM `currencies` WHERE `id` = '$id' ");
    if (!$row) {
        die(json_encode(['status' => 'error', 'msg' => __('Data does not exist')]));
    }
    $isUpdate = setCurrency($id);
    if ($isUpdate) {
        $data = json_encode([
            'status'    => 'success',
            'msg'       => __('Successful currency change')
        ]);
        die($data);
    }
}


if ($CMSNT->site('status_demo') != 0) {
    $data = json_encode([
        'status'    => 'error',
        'msg'       => __('This function cannot be used because this is a demo site')
    ]);
    die($data);
}
// CHỨC NĂNG KHÔNG DÙNG ĐƯỢC TẠI TRANG WEB DEMO

 

 

 

use PayPalCheckoutSdk\Core\PayPalHttpClient;
use PayPalCheckoutSdk\Core\SandboxEnvironment;
use PayPalCheckoutSdk\Core\ProductionEnvironment;
use PayPalCheckoutSdk\Orders\OrdersGetRequest;
use PayPalHttp\HttpException;

if($_POST['action'] == 'confirmPaypal' && isset($_POST['order']) ){

    if ($CMSNT->site('paypal_status') != 1) {
        die(json_encode(['status' => 'error', 'msg' => __('Chức năng này đang được bảo trì')]));
    }
    if (empty($_POST['token'])) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập')]));
    }
    if (!$getUser = $CMSNT->get_row("SELECT * FROM `users` WHERE `token` = '".check_string($_POST['token'])."' AND `banned` = 0 ")) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập')]));
    }
    $clientId = $CMSNT->site('paypal_clientId');
    $clientSecret = $CMSNT->site('paypal_clientSecret');
    $environment = new ProductionEnvironment($clientId, $clientSecret);
    //$environment = new SandboxEnvironment($clientId, $clientSecret);
    $client = new PayPalHttpClient($environment);
    $orderData = $_POST['order'];
    $request = new OrdersGetRequest($orderData['id']);
    try {
        $response = $client->execute($request);
        if ($response->statusCode != 200) {
            die(json_encode(['status' => 'error', 'msg' => __('Đã xảy ra lỗi!')]));
        }
        $order = $response->result;
        if ($order->status != 'COMPLETED') {
            die(json_encode(['status' => 'error', 'msg' => __('Đơn hàng không hợp lệ hoặc chưa thanh toán')]));
        }
        $orderDetail = $order->purchase_units[0];
        if ($CMSNT->num_rows("SELECT * FROM `payment_paypal` WHERE `trans_id` = '".$order->id."' ") > 0) {
            die(json_encode(['status' => 'error', 'msg' => __('Giao dịch này đã được xử lý')]));
        }
        $price = $CMSNT->site('paypal_rate') * $orderDetail->amount->value;
        $isInsert = $CMSNT->insert("payment_paypal", [
            'user_id'       => $getUser['id'],
            'trans_id'      => $order->id,
            'amount'        => $orderDetail->amount->value,
            'price'         => $price,
            'create_date'   => gettime(),
            'create_time'   => time()
        ]);
        if ($isInsert) {
            $user = new users();
            $isCong = $user->AddCredits($getUser['id'], $price, __('Nạp tiền tự động qua PayPal')." - $order->id", 'TOPUP_PAYPAL_'.$order->id);
            if($isCong){
                /** SEND NOTI CHO ADMIN */
                $my_text = $CMSNT->site('noti_recharge');
                $my_text = str_replace('{domain}', $_SERVER['SERVER_NAME'], $my_text);
                $my_text = str_replace('{username}', $getUser['username'], $my_text);
                $my_text = str_replace('{method}', 'PayPal', $my_text);
                $my_text = str_replace('{amount}', $orderDetail->amount->value, $my_text);
                $my_text = str_replace('{price}', $price, $my_text);
                $my_text = str_replace('{time}', gettime(), $my_text);
                sendMessAdmin($my_text);
                die(json_encode(['status' => 'success', 'msg' => __('Nạp tiền thành công')]));
            }else{
                die(json_encode(['status' => 'error', 'msg' => __('Hóa đơn này đã được cộng tiền rồi')]));
            }
        }
    } catch (HttpException $e) {
        die(json_encode(['status' => 'error', 'msg' => $e->getMessage()]));
    } catch (Exception $e) {
        die(json_encode(['status' => 'error', 'msg' => $e->getMessage()]));
    }
}

die(json_encode([
    'status'    => 'error',
    'msg'       => __('Invalid data')
]));