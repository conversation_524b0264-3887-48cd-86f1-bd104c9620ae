<?php

define("IN_SITE", true);
require_once(__DIR__."/../../config.php");
require_once(__DIR__."/../../libs/db.php");
require_once(__DIR__."/../../libs/lang.php");
require_once(__DIR__."/../../libs/helper.php");
require_once(__DIR__.'/../../models/is_admin.php');


if(!isset($_POST['action'])){
    $data = json_encode([
        'status'    => 'error',
        'msg'       => __('The Request Not Found')
    ]);
    die($data);   
}

  
 


if($_POST['action'] == 'phan_tich_utm_source_users'){
    if (empty($_POST['token'])) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')]));
    }
    if (!$getUser = $CMSNT->get_row("SELECT * FROM `users` WHERE `token` = '".check_string($_POST['token'])."' AND `banned` = 0 AND `admin` != 0 ")) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')]));
    }
    if(checkPermission($getUser['admin'], 'view_user') != true){
        die(json_encode(['status' => 'error', 'msg' => __('Bạn không có quyền sử dụng tính năng này')]));
    }
    // Tạo HTML cho tab
$html = '<ul class="nav nav-tabs mb-5 nav-justified nav-style-1 d-sm-flex d-block" id="myTab" role="tablist">';
$html .= '<li class="nav-item">';
$html .= '<a class="nav-link active" id="table-tab" data-toggle="tab" href="#table-content" role="tab" aria-controls="table-content" aria-selected="true">Table</a>';
$html .= '</li>';
$html .= '<li class="nav-item">';
$html .= '<a class="nav-link" id="chart-tab" data-toggle="tab" href="#chart-content" role="tab" aria-controls="chart-content" aria-selected="false">Pie Chart</a>';
$html .= '</li>';
$html .= '</ul>';

// Tạo HTML cho nội dung của tab
$html .= '<div class="tab-content" id="myTabContent">';
$html .= '<div class="tab-pane fade show active" id="table-content" role="tabpanel" aria-labelledby="table-tab">';
$html .= '<div class="table-responsive table-wrapper" style="max-height: 500px;overflow-y: auto;">';
$html .= '<table class="table text-nowrap table-striped table-hover table-bordered">
            <thead>
                <tr>
                    <th class="text-center">Xếp hạng</th>
                    <th class="text-center">utm_source</th>
                    <th class="text-center">Số thành viên đăng ký</th>
                </tr>
            </thead>
            <tbody>';
$i = 1;
$data_labels = [];
$data_user_counts = [];
foreach($CMSNT->get_list("SELECT 
    utm_source, 
    COUNT(*) AS total_users
FROM users 
GROUP BY utm_source 
ORDER BY total_users DESC ") as $row){
    $data_labels[] = $row['utm_source'];
    $data_user_counts[] = $row['total_users'];
    $html .= "<tr>
    <td class='text-center' style='font-size:15px;'>" . $i++ . "</td>
    <td class='text-center'>" . $row['utm_source'] . "</td>
    <td class='text-center'><b>" . format_cash($row['total_users']) . "</b></td>
  </tr>";
}
$html .= "</tbody>
        </table>";
$html .= "</div>";
$html .= '</div>';

$html .= '<div class="tab-pane fade" id="chart-content" role="tabpanel" aria-labelledby="chart-tab">';
$html .= '<canvas id="myChart" width="500" height="300"></canvas>';
$html .= '</div>';

$html .= '</div>';

// Thêm kịch bản JavaScript để chuyển đổi tab
$html .= '<script>
            $(document).ready(function(){
                $("#table-tab").click(function(){
                    $("#chart-content").removeClass("show active");
                    $("#chart-tab").removeClass("active");
                    $("#table-content").addClass("show active");
                    $("#table-tab").addClass("active");
                });
                $("#chart-tab").click(function(){
                    $("#table-content").removeClass("show active");
                    $("#table-tab").removeClass("active");
                    $("#chart-content").addClass("show active");
                    $("#chart-tab").addClass("active");
                    // Thêm kịch bản JavaScript để vẽ biểu đồ Pie Chart
                    var ctx = document.getElementById("myChart").getContext("2d");
                    var myChart = new Chart(ctx, {
                        type: "pie",
                        data: {
                            labels: '.json_encode($data_labels).',
                            datasets: [{
                                label: "Số lượng người dùng",
                                data: '.json_encode($data_user_counts).',
                                backgroundColor: [
                                    "rgba(255, 99, 132, 0.6)",
                                    "rgba(54, 162, 235, 0.6)",
                                    "rgba(255, 206, 86, 0.6)",
                                    "rgba(75, 192, 192, 0.6)",
                                    "rgba(153, 102, 255, 0.6)",
                                    "rgba(255, 159, 64, 0.6)"
                                ],
                                borderColor: [
                                    "rgba(255, 99, 132, 1)",
                                    "rgba(54, 162, 235, 1)",
                                    "rgba(255, 206, 86, 1)",
                                    "rgba(75, 192, 192, 1)",
                                    "rgba(153, 102, 255, 1)",
                                    "rgba(255, 159, 64, 1)"
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            legend: {
                                position: "right",
                                labels: {
                                    fontColor: "black",
                                    fontSize: 12
                                }
                            }
                        }
                    });
                });
            });
        </script>';




    


    die($html);
}


if($_POST['action'] == 'view_nap_tien_gan_day'){
    if (empty($_POST['token'])) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')]));
    }
    if (!$getUser = $CMSNT->get_row("SELECT * FROM `users` WHERE `token` = '".check_string($_POST['token'])."' AND `banned` = 0 AND `admin` != 0 ")) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')]));
    }
    if(checkPermission($getUser['admin'], 'view_recent_transactions') != true){
        die(json_encode(['status' => 'error', 'msg' => __('Bạn không có quyền sử dụng tính năng này')]));
    }
    $deposits = $CMSNT->get_list("SELECT * FROM `deposit_log` WHERE `is_virtual` = 0 ORDER BY id DESC limit 100");
    $html = '';
    foreach($deposits as $deposit){
        $html .= '<li>
        <div class="timeline-time text-end">
            <span class="date">'.timeAgo($deposit['create_time']).'</span>
        </div>
        <div class="timeline-icon">
            <a href="javascript:void(0);"></a>
        </div>
        <div class="timeline-body">
            <div class="d-flex align-items-top timeline-main-content flex-wrap mt-0">
                <div class="flex-fill">
                    <div class="d-flex align-items-center">
                        <div class="mt-sm-0 mt-2">
                            <p class="mb-0 text-muted"><b style="color: green;">'.getRowRealtime('users', $deposit['user_id'], 'username').'</b>
                                thực hiện nạp <b style="color: blue;">'.format_currency($deposit['amount']).'</b>
                                bằng <b style="color:red">'.$deposit['method'].'</b> thực nhận <b style="color:blue;">'.format_currency($deposit['received']).'</b>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </li>';
    }
    die($html); 
}
if($_POST['action'] == 'view_don_hang_gan_day'){
    if (empty($_POST['token'])) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')]));
    }
    if (!$getUser = $CMSNT->get_row("SELECT * FROM `users` WHERE `token` = '".check_string($_POST['token'])."' AND `banned` = 0 AND `admin` != 0 ")) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')]));
    }
    if(checkPermission($getUser['admin'], 'view_recent_transactions') != true){
        die(json_encode(['status' => 'error', 'msg' => __('Bạn không có quyền sử dụng tính năng này')]));
    }
    $orders = $CMSNT->get_list("SELECT * FROM `cron_jobs` ORDER BY id DESC limit 100");
    $html = '';
    foreach($orders as $order){
        $html .= '<li>
            <div class="timeline-time text-end">
                <span class="date">'.timeAgo(strtotime($order['created_at'])).'</span>
            </div>
            <div class="timeline-icon">
                <a href="javascript:void(0);"></a>
            </div>
            <div class="timeline-body">
                <div class="d-flex align-items-top timeline-main-content flex-wrap mt-0">
                    <div class="flex-fill">
                        <div class="d-flex align-items-center">
                            <div class="mt-sm-0 mt-2">
                                <p class="mb-0 text-muted"><b style="color: green;">'.getRowRealtime('users', $order['user_id'], 'username').'</b>
                                    tạo đơn hàng CRON link
                                    <b>'.$order['link_cron'].'</b> với giá <b style="color:blue;">'.format_currency($order['pay']).'</b>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </li>';
    }
    die($html);  
}
 

 
 
 