<?php

define("IN_SITE", true);
require_once(__DIR__."/../../config.php");
require_once(__DIR__."/../../libs/db.php");
require_once(__DIR__."/../../libs/lang.php");
require_once(__DIR__."/../../libs/helper.php");
require_once(__DIR__."/../../libs/sendEmail.php");
require_once(__DIR__.'/../../libs/database/users.php');
$Mobile_Detect = new Mobile_Detect();

if ($CMSNT->site('status') != 1) {
    $data = json_encode([
        'status'    => 'error',
        'msg'       => __('Hệ thống đang bảo trì!')
    ]);
    die($data);
}
if(!isset($_REQUEST['action'])){
    $data = json_encode([
        'status'    => 'error',
        'msg'       => __('The Request Not Found')
    ]);
    die($data);   
}
if($_POST['action'] == 'totalPayment'){
    if (empty($_POST['token'])) {
        die(json_encode([
            'status' => 'success',
            'money' => __('Vui lòng đăng nhập')
        ]));
    }
    if (!$getUser = $CMSNT->get_row("SELECT * FROM `users` WHERE `token` = '".check_string($_POST['token'])."' AND `banned` = 0 ")) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vui lòng đăng nhập')
        ]));
    }
    if (empty($_POST['server_id'])) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vui lòng chọn máy chủ')
        ]));
    }
    $server_id = check_string($_POST['server_id']);
    if (!$server = $CMSNT->get_row(" SELECT * FROM `cron_server` WHERE `id` = '$server_id' AND `status` = 1 ")) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Máy chủ này không tồn tại trong hệ thống, vui lòng chọn máy chủ khác')
        ]));
    }
    if (empty($_POST['rental_period'])) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vui lòng chọn thời gian thuê')
        ]));
    }
    $rental_period = check_string($_POST['rental_period']);
    if (!ctype_digit($rental_period)) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Thời gian thuê không hợp lệ')
        ]));
    }
    // Lấy các chu kỳ hợp lệ từ cơ sở dữ liệu
    $valid_periods = [];
    $options = explode(PHP_EOL, $CMSNT->site('cronjob_billing_cycle'));
    if (is_array($options) && count($options) > 0) {
        foreach ($options as $option) {
            $option_renew = explode('|', $option);
            $valid_periods[] = $option_renew[0];
        }
    }
    // Kiểm tra nếu chu kỳ thuê không nằm trong danh sách hợp lệ
    if (!in_array($rental_period, $valid_periods)) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Thời gian thuê không hợp lệ')
        ]));
    }
    //
    $discount = 0;
    $price = $server['price'] / 30; // đơn giá tính theo ngày.
    $pay = $price * $rental_period; // đơn giá * số ngày thuê.
    // Xử lý giảm giá bằng chiết khấu
    if($getUser['discount'] != 0){
        $discount = $pay * $getUser['discount'] / 100;
    }
    $pay = $pay - $discount;
    die(json_encode([
        'status'    => 'success',
        'money'     => format_currency($pay)
    ]));

}

if($_POST['action'] == 'AddCronJob'){
    if ($CMSNT->site('status_demo') != 0) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('This function cannot be used because this is a demo site')
        ]));
    }
    if (empty($_POST['token'])) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vui lòng đăng nhập')
        ]));
    }
    if (!$getUser = $CMSNT->get_row("SELECT * FROM `users` WHERE `token` = '".check_string($_POST['token'])."' AND `banned` = 0 ")) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vui lòng đăng nhập')
        ]));
    }
    //
    if (empty($_POST['link_cron'])) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vui lòng nhập liên kết cần CRON')
        ]));
    }
    $link_cron = check_string($_POST['link_cron']);
    if (!isValidUrl($link_cron)) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Liên kết CRON không hợp lệ')
        ]));
    }
    //
    if (empty($_POST['method'])) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vui lòng nhập Method')
        ]));
    }
    $method = check_string($_POST['method']);
    if ($method != 'POST' && $method != 'GET') {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Method không hợp lệ')
        ]));
    }
    //
    if (empty($_POST['server_id'])) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vui lòng chọn máy chủ')
        ]));
    }
    $server_id = check_string($_POST['server_id']);
    if (!$server = $CMSNT->get_row(" SELECT * FROM `cron_server` WHERE `id` = '$server_id' AND `status` = 1 ")) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Máy chủ này không tồn tại trong hệ thống, vui lòng chọn máy chủ khác')
        ]));
    }
    if ($CMSNT->num_rows(" SELECT `id` FROM `cron_jobs` WHERE `server_id` = '$server_id' ") >= $server['max']) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Máy chủ này đã đạt giới hạn, vui lòng chọn máy chủ khác')
        ]));
    }
    //
    if (empty($_POST['timeloop'])) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vui lòng nhập vòng lặp')
        ]));
    }
    $timeloop = check_string($_POST['timeloop']);
    if (!ctype_digit($timeloop)) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vòng lặp không hợp lệ')
        ]));
    }
    if ($timeloop < intval($server['timeloop'])) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vòng lặp tối thiểu lớn hơn hoặc bằng '.$server['timeloop'].' giây')
        ]));
    }
    //
    if (empty($_POST['rental_period'])) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vui lòng chọn thời gian thuê')
        ]));
    }
    $rental_period = check_string($_POST['rental_period']);
    if (!ctype_digit($rental_period)) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Thời gian thuê không hợp lệ')
        ]));
    }

    // Lấy các chu kỳ hợp lệ từ cơ sở dữ liệu
    $valid_periods = [];
    $options = explode(PHP_EOL, $CMSNT->site('cronjob_billing_cycle'));
    if (is_array($options) && count($options) > 0) {
        foreach ($options as $option) {
            $option_renew = explode('|', $option);
            $valid_periods[] = $option_renew[0];
        }
    }
    // Kiểm tra nếu chu kỳ thuê không nằm trong danh sách hợp lệ
    if (!in_array($rental_period, $valid_periods)) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Thời gian thuê không hợp lệ')
        ]));
    }
    //
    $discount = 0;
    $trans_id = random('QWERTYUOPASDFGHJKZXCVBNM123456789', 3).uniqid();
    $price = $server['price'] / 30; // đơn giá tính theo ngày.
    if($server['price'] <= 0){
        $price = 0;
    }
    $pay = $price * $rental_period; // đơn giá * số ngày thuê.
    // Xử lý giảm giá bằng chiết khấu
    if($getUser['discount'] != 0){
        $discount = $pay * $getUser['discount'] / 100;
    }
    $pay = $pay - $discount;
    //
    if (getRowRealtime('users', $getUser['id'], 'money') < $pay) {
        die(json_encode([
            'status' => 'error', 
            'msg' => __('Số dư không đủ, vui lòng nạp thêm')
        ]));
    }
    $User = new users();
    $isTru = $User->RemoveCredits($getUser['id'], $pay, __('Thanh toán đơn hàng ').' #'.$trans_id, 'ORDER_'.$trans_id);
    if($isTru){

        // Lấy thời gian hiện tại
        $current_time = time();
        // Cộng ngày vào thời gian hiện tại
        $expiration_time = date('Y/m/d H:i:s', strtotime("+$rental_period days", $current_time));

        $isInsert = $CMSNT->insert('cron_jobs', [
            'user_id'       => $getUser['id'],
            'trans_id'      => $trans_id,
            'server_id'     => $server_id,
            'link_cron'     => $link_cron,
            'method'        => $method,
            'timeloop'      => $timeloop,
            'status'        => 'active',
            'created_at'    => gettime(),
            'updated_at'    => gettime(),
            'expires_at'    => $expiration_time,
            'last_run'      => 0,
            'pay'           => $pay
        ]);
        if($isInsert){
            // CỘNG HOA HỒNG
            if($CMSNT->site('affiliate_status') == 1 && $getUser['ref_id'] != 0){
                $ck = $CMSNT->site('affiliate_ck');
                if(getRowRealtime('users', $getUser['ref_id'], 'ref_ck') != 0){
                    $ck = getRowRealtime('users', $getUser['ref_id'], 'ref_ck');
                }
                $price = $pay * $ck / 100;
                $User->AddCommission($getUser['ref_id'], $getUser['id'], $price, __('Hoa hồng thành viên'.' '.$getUser['username']));
            }

            die(json_encode([
                'status' => 'success', 
                'msg' => __('Thanh toán thành công!')
            ]));
        }else{
            $User->RefundCredits($getUser['id'], $pay, __('Hoàn tiền đơn hàng').' #'.$trans_id, 'REFUND_'.$trans_id);
            die(json_encode([
                'status' => 'error', 
                'msg' => __('Giao dịch thất bại, vui lòng thử lại hoặc liên hệ Admin')
            ]));
        }
    }
}

if($_POST['action'] == 'renewCronJob'){
    if ($CMSNT->site('status_demo') != 0) {
        die(json_encode([
            'status' => 'error', 
            'msg' => __('This function cannot be used because this is a demo site')
        ]));
    }
    if (empty($_POST['token'])) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vui lòng đăng nhập')
        ]));
    }
    if (!$getUser = $CMSNT->get_row("SELECT * FROM `users` WHERE `token` = '" . check_string($_POST['token']) . "' AND `banned` = 0 ")) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vui lòng đăng nhập')
        ]));
    }
    if (empty($_POST['trans_id'])) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vui lòng nhập mã đơn hàng')
        ]));
    }
    $trans_id = check_string($_POST['trans_id']);
    if (!$cron = $CMSNT->get_row(" SELECT * FROM `cron_jobs` WHERE `trans_id` = '$trans_id' AND `user_id` = '" . $getUser['id'] . "' ")) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Dữ liệu không tồn tại trong hệ thống')
        ]));
    }
    if (!$server = $CMSNT->get_row(" SELECT * FROM `cron_server` WHERE `id` = '" . $cron['server_id'] . "' AND `status` = 1 ")) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Máy chủ này không tồn tại trong hệ thống, vui lòng chọn máy chủ khác')
        ]));
    }
    //
    if (empty($_POST['rental_period'])) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Vui lòng chọn thời gian thuê')
        ]));
    }
    $rental_period = check_string($_POST['rental_period']);
    if (!ctype_digit($rental_period)) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Thời gian thuê không hợp lệ')
        ]));
    }    
    // Lấy các chu kỳ hợp lệ từ cơ sở dữ liệu
    $valid_periods = [];
    $options = explode(PHP_EOL, $CMSNT->site('cronjob_billing_cycle'));
    if (is_array($options) && count($options) > 0) {
        foreach ($options as $option) {
            $option_renew = explode('|', $option);
            $valid_periods[] = $option_renew[0];
        }
    }
    // Kiểm tra nếu chu kỳ thuê không nằm trong danh sách hợp lệ
    if (!in_array($rental_period, $valid_periods)) {
        die(json_encode([
            'status' => 'error', 
            'msg' => __('Thời gian thuê không hợp lệ')
        ]));
    }
    //
    $discount = 0;
    $price = $server['price'] / 30; // đơn giá tính theo ngày.
    if($server['price'] <= 0){
        $price = 0;
    }
    $pay = $price * $rental_period; // đơn giá * số ngày thuê.
    // Xử lý giảm giá bằng chiết khấu
    if($getUser['discount'] != 0){
        $discount = $pay * $getUser['discount'] / 100;
    }
    $pay = $pay - $discount;
    //
    if (getRowRealtime('users', $getUser['id'], 'money') < $pay) {
        die(json_encode([
            'status' => 'error', 
            'msg' => __('Số dư không đủ, vui lòng nạp thêm')
        ]));
    }
    $User = new users();
    $isTru = $User->RemoveCredits($getUser['id'], $pay, __('Thanh toán gia hạn đơn hàng').' #'.$trans_id, 'RENEW_'.$trans_id.'_'.uniqid());
    if($isTru){

        // Lấy thời gian hết hạn cuối cùng
        $current_time = $cron['expires_at'];
        // Cộng thêm ngày vào thời gian hết hạn
        $expiration_time = date('Y/m/d H:i:s', strtotime("+$rental_period days", strtotime($current_time)));
        $total_payment = $cron['pay'] + $pay; // Tính tổng số tiền đã thanh toán cho link CRON
        $isUpdate = $CMSNT->update('cron_jobs', [
            'user_id'       => $getUser['id'],
            'status'        => 'active',
            'updated_at'    => gettime(),
            'expires_at'    => $expiration_time,
            'pay'           => $total_payment
        ], " `id` = '".$cron['id']."' ");
        if($isUpdate){
            // CỘNG HOA HỒNG
            if($CMSNT->site('affiliate_status') == 1 && $getUser['ref_id'] != 0){
                $ck = $CMSNT->site('affiliate_ck');
                if(getRowRealtime('users', $getUser['ref_id'], 'ref_ck') != 0){
                    $ck = getRowRealtime('users', $getUser['ref_id'], 'ref_ck');
                }
                $price = $pay * $ck / 100;
                $User->AddCommission($getUser['ref_id'], $getUser['id'], $price, __('Hoa hồng thành viên'.' '.$getUser['username']));
            }
            die(json_encode(['status' => 'success', 'msg' => __('Gia hạn thành công!')]));
        }else{
            $User->RefundCredits($getUser['id'], $pay, __('Hoàn tiền đơn hàng').' #'.$trans_id, 'REFUND_'.$trans_id);
            die(json_encode(['status' => 'error', 'msg' => __('Giao dịch thất bại, vui lòng thử lại hoặc liên hệ Admin')]));
        }
    }
}

if($_POST['action'] == 'saveCronJob'){
    if ($CMSNT->site('status_demo') != 0) {
        die(json_encode(['status' => 'error', 'msg' => __('This function cannot be used because this is a demo site')]));
    }
    if (empty($_POST['token'])) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập')]));
    }
    if (!$getUser = $CMSNT->get_row("SELECT * FROM `users` WHERE `token` = '".check_string($_POST['token'])."' AND `banned` = 0 ")) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập')]));
    }
    if (empty($_POST['id'])) {
        die(json_encode(['status' => 'error', 'msg' => __('Dữ liệu không hợp lệ')]));
    }
    if (!$cron = $CMSNT->get_row("SELECT * FROM `cron_jobs` WHERE `trans_id` = '".check_string($_POST['id'])."' AND `user_id` = '".$getUser['id']."' ")) {
        die(json_encode(['status' => 'error', 'msg' => __('Dữ liệu không hợp lệ')]));
    }
    if (!$server = $CMSNT->get_row(" SELECT * FROM `cron_server` WHERE `id` = '".$cron['server_id']."' AND `status` = 1 ")) {
        die(json_encode([
            'status' => 'error',
            'msg' => __('Máy chủ này không tồn tại trong hệ thống, vui lòng chọn máy chủ khác')
        ]));
    }
    //
    if (empty($_POST['link_cron'])) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng nhập liên kết cần CRON')]));
    }
    $link_cron = check_string($_POST['link_cron']);
    if(!isValidUrl($link_cron)){
        die(json_encode(['status' => 'error', 'msg' => __('Liên kết CRON không hợp lệ')]));
    }
    //
    if (empty($_POST['timeloop'])) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng nhập vòng lặp')]));
    }
    $timeloop = check_string($_POST['timeloop']);
    if(!ctype_digit($timeloop)){
        die(json_encode(['status' => 'error', 'msg' => __('Vòng lặp không hợp lệ')]));
    }
    if($timeloop < intval($server['timeloop'])){
        die(json_encode(['status' => 'error', 'msg' => __('Vòng lặp tối thiểu lớn hơn hoặc bằng '.$server['timeloop'].' giây')]));
    }
    //
    if (empty($_POST['method'])) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng nhập Method')]));
    }
    $method = check_string($_POST['method']);
    if($method != 'POST' && $method != 'GET'){
        die(json_encode(['status' => 'error', 'msg' => __('Method không hợp lệ')]));
    }
    $isInsert = $CMSNT->update('cron_jobs', [
        'link_cron'     => $link_cron,
        'method'        => $method,
        'timeloop'      => $timeloop,
        'updated_at'    => gettime(),
    ], " `id` = '".$cron['id']."' ");
    if($isInsert){
        $Mobile_Detect = new Mobile_Detect();
        $CMSNT->insert("logs", [
            'user_id'       => $getUser['id'],
            'ip'            => myip(),
            'device'        => $Mobile_Detect->getUserAgent(),
            'createdate'    => gettime(),
            'action'        => __('Chỉnh sửa liên kết CRON').' ('.$cron['trans_id'].')'
        ]);
        die(json_encode(['status' => 'success', 'msg' => __('Cập nhật thành công!')]));
    }else{
        die(json_encode(['status' => 'error', 'msg' => __('Cập nhật thất bại!')]));
    }
}

if($_POST['action'] == 'activeCron'){
    if (empty($_POST['id'])) {
        die(json_encode(['status' => 'error', 'msg' => __('ID không tồn tại trong hệ thống')]));
    }
    if (empty($_POST['token'])) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')]));
    }
    if (!$getUser = $CMSNT->get_row("SELECT * FROM `users` WHERE `token` = '".check_string($_POST['token'])."' AND `banned` = 0 ")) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')]));
    }
    $id = check_string($_POST['id']);
    if (!$cron = $CMSNT->get_row("SELECT * FROM `cron_jobs` WHERE `id` = '$id' AND `user_id` = ".$getUser['id']." ")) {
        die(json_encode(['status' => 'error', 'msg' => __('Dữ liệu không tại trong hệ thống')]));
    }
    $isUpdate = $CMSNT->update('cron_jobs', [
        'status'        => 'active',
        'updated_at'    => gettime()
    ], " `id` = '".$cron['id']."' ");
    if($isUpdate){
        $Mobile_Detect = new Mobile_Detect();
        $CMSNT->insert("logs", [
            'user_id'       => $getUser['id'],
            'ip'            => myip(),
            'device'        => $Mobile_Detect->getUserAgent(),
            'createdate'    => gettime(),
            'action'        => __('Kích hoạt lại CRON').' ('.$cron['link_cron'].')'
        ]);
        die(json_encode([
            'status'    => 'success',
            'msg'       => __('Kích hoạt thành công!')
        ]));
    }else{
        die(json_encode([
            'status'    => 'error',
            'msg'       => __('Kích hoạt thất bại')
        ]));
    }
}

if($_POST['action'] == 'pauseCron'){
    if (empty($_POST['id'])) {
        die(json_encode(['status' => 'error', 'msg' => __('ID không tồn tại trong hệ thống')]));
    }
    if (empty($_POST['token'])) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')]));
    }
    if (!$getUser = $CMSNT->get_row("SELECT * FROM `users` WHERE `token` = '".check_string($_POST['token'])."' AND `banned` = 0 ")) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')]));
    }
    $id = check_string($_POST['id']);
    if (!$cron = $CMSNT->get_row("SELECT * FROM `cron_jobs` WHERE `id` = '$id' AND `user_id` = ".$getUser['id']." ")) {
        die(json_encode(['status' => 'error', 'msg' => __('Dữ liệu không tại trong hệ thống')]));
    }
    $isUpdate = $CMSNT->update('cron_jobs', [
        'status'        => 'pause',
        'updated_at'    => gettime()
    ], " `id` = '".$cron['id']."' ");
    if($isUpdate){
        $Mobile_Detect = new Mobile_Detect();
        $CMSNT->insert("logs", [
            'user_id'       => $getUser['id'],
            'ip'            => myip(),
            'device'        => $Mobile_Detect->getUserAgent(),
            'createdate'    => gettime(),
            'action'        => __('Tạm dừng CRON').' ('.$cron['link_cron'].')'
        ]);
        die(json_encode([
            'status'    => 'success',
            'msg'       => __('Dừng chạy thành công!')
        ]));
    }else{
        die(json_encode([
            'status'    => 'error',
            'msg'       => __('Dừng chạy thất bại')
        ]));
    }
}

if($_POST['action'] == 'HistoryCron'){
    if (empty($_POST['token'])) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')]));
    }
    if (!$getUser = $CMSNT->get_row("SELECT * FROM users WHERE token = '".check_string($_POST['token'])."' AND banned = 0 ")) {
        die(json_encode(['status' => 'error', 'msg' => __('Vui lòng đăng nhập để sử dụng tính năng này')]));
    }
    if(!$cron = $CMSNT->get_row(" SELECT * FROM cron_jobs WHERE id = '".check_string($_POST['id'])."' AND user_id = '".$getUser['id']."' ")){
        die(json_encode(['status' => 'error', 'msg' => __('Dữ liệu không hợp lệ')]));
    }
    // Lấy dữ liệu từ cron_history
    $history = $CMSNT->get_list("SELECT * FROM cron_history WHERE cron_job_id = '".$cron['id']."' ORDER BY id DESC");
    $response = [
        'status' => 'success',
        'cron' => [
            'link' => $cron['link_cron']
        ],
        'history' => []
    ];
    // Chuẩn bị dữ liệu JSON
    foreach ($history as $row) {
        $response['history'][] = [
            'run_at'    => $row['run_at'],
            'time_ago'  => timeAgo(strtotime($row['run_at'])),
            'status'    => display_cron_jobs($row['code']),
            'timeout'   => $row['timeout'],
            'output'    => $row['output']
        ];
    }
    // Trả về JSON
    die(json_encode($response));

}
die(json_encode([
    'status'    => 'error',
    'msg'       => __('Request does not exist')
]));