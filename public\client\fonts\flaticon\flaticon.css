	/*
  	Flaticon icon font: Flaticon
  	Creation date: 08/03/2021 13:24
  	*/

@font-face {
  font-family: "Flaticon";
  src: url("Flaticon.eot");
  src: url("Flaticon%EF%B9%96.eot#iefix") format("embedded-opentype"),
       url("Flaticon.woff2") format("woff2"),
       url("Flaticon.woff") format("woff"),
       url("Flaticon.ttf") format("truetype"),
       url("Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("Flaticon.svg#Flaticon") format("svg");
  }
}

[class^="flaticon-"]:before, [class*=" flaticon-"]:before,
[class^="flaticon-"]:after, [class*=" flaticon-"]:after {   
  font-family: Flaticon;
  font-style: normal;
}

.flaticon-vegetable:before { content: "\f100"; }
.flaticon-fruit:before { content: "\f101"; }
.flaticon-groceries:before { content: "\f102"; }
.flaticon-bread:before { content: "\f103"; }
.flaticon-fast-food:before { content: "\f104"; }
.flaticon-fish:before { content: "\f105"; }
.flaticon-barbecue:before { content: "\f106"; }
.flaticon-crab:before { content: "\f107"; }
.flaticon-salad:before { content: "\f108"; }
.flaticon-dried-fruit:before { content: "\f109"; }
.flaticon-wheat-sack:before { content: "\f10a"; }
.flaticon-beverage:before { content: "\f10b"; }
.flaticon-cheers:before { content: "\f10c"; }
.flaticon-dairy-products:before { content: "\f10d"; }